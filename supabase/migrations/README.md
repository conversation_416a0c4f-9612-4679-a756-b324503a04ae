# Migraciones de PokéCollector

Este directorio contiene las migraciones de la base de datos para PokéCollector.

## Archivos de Migración

### ✅ Migración Consolidada (Recomendada para Nuevas Instalaciones)

- **`20250120000020_consolidated_complete_schema.sql`** - Esquema completo consolidado que incluye:
  - Todas las tablas principales (users, collections, collection_cards, wishlist_cards, subscriptions)
  - Funcionalidades de administración completas (audit_logs, user_statistics, subscription_overrides)
  - Tablas de soporte (webhook_events, subscription_changes)
  - Funciones optimizadas con contexto de seguridad apropiado
  - Políticas RLS no recursivas y eficientes
  - Índices de rendimiento estratégicos
  - Triggers automáticos para estadísticas
  - Configuración completa de permisos y realtime

### 📁 Migraciones Anteriores (Supersedidas por la Consolidada)

- `20240602000001_consolidated_schema_final.sql` - Esquema principal original
- `20250120000010_consolidated_admin_fixes.sql` - Funcionalidades de administración
- `20250120000011_fix_trigger_schema_issues.sql` - Correcciones de triggers

**Nota**: Para nuevas instalaciones, use únicamente la migración consolidada. Las migraciones anteriores se mantienen para compatibilidad con instalaciones existentes.

## Optimizaciones Aplicadas

### Problemas Identificados y Solucionados:

1. **Funciones Duplicadas**: Se consolidaron las definiciones de `update_user_statistics`
2. **Lógica de Triggers Redundante**: Se eliminó la duplicación de funciones de trigger
3. **Compatibilidad de Columnas**: Se simplificó el soporte dual de columnas en audit_logs
4. **Conflictos de Políticas RLS**: Se organizaron las políticas para evitar conflictos
5. **Valores Hardcodeados**: Se documentaron los valores que requieren configuración

### Beneficios de la Consolidación:

- **Instalación Simplificada**: Una sola migración para nuevas instalaciones
- **Mejor Rendimiento**: Funciones optimizadas y estrategia de índices mejorada
- **Mantenimiento Reducido**: Eliminación de redundancias y duplicaciones
- **Seguridad Mejorada**: Políticas RLS no recursivas y contextos de seguridad apropiados
- **Documentación Clara**: Comentarios completos y estructura organizada

### Recomendaciones de Mantenimiento:

- **Nuevas Instalaciones**: Usar únicamente la migración consolidada `20250120000020`
- **Instalaciones Existentes**: Mantener las migraciones actuales (funcionan perfectamente)
- Revisar periódicamente los índices de rendimiento
- Monitorear el crecimiento de la tabla audit_logs para implementar archivado si es necesario
- Considerar migrar a la versión consolidada en el próximo reset de desarrollo

## Importante: No Creación Automática de Colecciones

Esta migración está diseñada específicamente para **NO crear colecciones automáticamente** cuando un usuario se registra. Las colecciones deben ser creadas manualmente por el usuario a través de la interfaz de la aplicación.

## Cómo Aplicar la Migración

Para aplicar esta migración, sigue estos pasos:

1. Resetea la base de datos de Supabase desde el panel de control:

   - Ve a la sección "Database" en el panel de Supabase
   - Busca la opción "Reset Database" y confirma la acción
   - Esto eliminará todos los datos existentes

2. Aplica la migración:

   ```bash
   supabase db reset
   ```

3. Verifica que la migración se haya aplicado correctamente:
   - Comprueba que las tablas se hayan creado
   - Verifica que las políticas de RLS estén configuradas
   - Asegúrate de que no se creen colecciones automáticamente al registrar un usuario

## Solución de Problemas

### Errores comunes al aplicar la migración

#### Error 1: Parámetro de función

Si encuentras un error como este al aplicar la migración:

```
ERROR:  42P13: cannot change name of input parameter "user_id_param"
HINT:  Use DROP FUNCTION delete_user_data(uuid) first.
```

Este error ocurre porque la función ya existe con un nombre de parámetro diferente. La migración ya incluye el código necesario para eliminar la función antes de recrearla, pero si aún así encuentras este error, puedes:

1. Conectarte directamente a la base de datos y ejecutar: `DROP FUNCTION IF EXISTS public.delete_user_data(UUID);`
2. Luego aplicar la migración nuevamente

#### Error 2: Relación no existe

Si encuentras un error como este:

```
ERROR: relation "public.users" does not exist (SQLSTATE 42P01)
At statement 2:
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users
```

Este error ocurre cuando intentamos eliminar un trigger de una tabla que aún no existe. La migración ha sido actualizada para evitar este problema, asegurándose de eliminar primero las funciones y luego las tablas (lo que también eliminará los triggers asociados).

### Creación automática de colecciones

Si sigues experimentando la creación automática de colecciones después de aplicar esta migración:

1. Verifica si hay Edge Functions que puedan estar creando colecciones
2. Comprueba si hay webhooks configurados que puedan estar creando colecciones
3. Revisa el código del frontend para asegurarte de que no haya llamadas automáticas a la API para crear colecciones
4. Revisa las funciones `initialize-user` en Supabase Edge Functions

Si el problema persiste, considera revisar los logs de la base de datos para identificar qué está creando las colecciones.
